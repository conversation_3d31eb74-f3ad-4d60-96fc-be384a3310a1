from typing import List, Set
from fastapi import Depends, HTTPException, Request, status


class RBAC:
    def __init__(self, required_permissions: List[str]):
        self.required_permissions = set(required_permissions)

    def __call__(self, request: Request) -> None:
        # API Gateway puts the authorizer context here
        authorizer_context = request.scope.get('aws.event', {}).get('requestContext', {}).get('authorizer', {})
        
        if not authorizer_context:
            # This should not happen if the authorizer is configured correctly
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authorizer context not found."
            )

        # Retrieve the permissions string and split it back into a set
        permissions_str = authorizer_context.get('permissions', '')
        user_permissions = set(permissions_str.split(',')) if permissions_str else set()

        # Check if the user has ALL the required permissions
        if not self.required_permissions.issubset(user_permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {list(self.required_permissions - user_permissions)}"
            )


# A more user-friendly alias for the class
def requires(permissions: List[str]):
    return Depends(RBAC(permissions))
