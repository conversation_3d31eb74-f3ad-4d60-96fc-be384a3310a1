from typing import Optional
from pydantic import Field
from .base import BaseSchema


class Token(BaseSchema):
    """Schema for token response when issuing a token."""
    
    access_token: str = Field(..., description="JWT access token")
    refresh_token: Optional[str] = Field(None, description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")


class TokenData(BaseSchema):
    """Schema representing the decoded payload of a JWT token."""
    
    sub: Optional[str] = Field(None, description="Subject (username) from JWT")
    role: Optional[str] = Field(None, description="User role from JWT")
    exp: Optional[int] = Field(None, description="Token expiration timestamp")
    iat: Optional[int] = Field(None, description="Token issued at timestamp")


class TokenRefresh(BaseSchema):
    """Schema for token refresh request."""
    
    refresh_token: str = Field(..., description="Refresh token to exchange for new access token")
