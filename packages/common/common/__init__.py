"""
Common module for EyeCue API services.

This module provides shared functionality including:
- Authentication and authorization (RBAC)
- Database connections
- Response schemas
- Token handling
"""

from .security.rbac import (
    requires,
    requires_reports_access,
    requires_vision_config_access,
    requires_auth0_user,
    Auth0User,
    Auth0RB<PERSON>,
    RBAC
)

from .schemas.token import (
    Token,
    TokenData,
    TokenRefresh,
    Auth0Claims,
    ResourceAccess
)

from .schemas.response import (
    BaseResponse
)

from .schemas.base import (
    BaseSchema,
    TimestampMixin,
    UUIDMixin
)

__all__ = [
    # RBAC
    "requires",
    "requires_reports_access", 
    "requires_vision_config_access",
    "requires_auth0_user",
    "Auth0User",
    "Auth0RBAC",
    "RBAC",
    
    # Token schemas
    "Token",
    "TokenData", 
    "TokenRefresh",
    "Auth0Claims",
    "ResourceAccess",
    
    # Response schemas
    "BaseResponse",
    
    # Base schemas
    "BaseSchema",
    "TimestampMixin",
    "UUIDMixin",
]
