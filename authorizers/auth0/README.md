# Auth0 Lambda Authorizer

This Lambda function serves as an API Gateway authorizer that validates Auth0 JWT tokens and implements Role-Based Access Control (RBAC) using custom claims.

## Features

- **JWT Validation**: Validates Auth0 JWT tokens using JWKS
- **Custom Claims Support**: Parses and evaluates Auth0 custom claims for authorization
- **Resource-Level Access**: Generates fine-grained IAM policies based on user permissions
- **Flexible RBAC**: Supports customer/site-based access control for reports and vision config

## Custom Claims Structure

The authorizer expects JWT tokens with custom claims in the following format:

```json
{
  "sub": "user-123",
  "full-access:reports": {
    "customer1": ["site1", "site2"],
    "customer2": ["*"]
  },
  "full-access:vision-config": {
    "locked-in": true
  }
}
```

### Claims Explanation

- `full-access:reports`: Defines access to reports by customer and site
  - Key: Customer identifier
  - Value: Array of site identifiers or `["*"]` for all sites
- `full-access:vision-config`: Defines access to vision configuration
  - `locked-in`: <PERSON><PERSON>an indicating if user has full access

## Environment Variables

Required environment variables:

- `AUTH0_DOMAIN`: Your Auth0 domain (e.g., `your-tenant.auth0.com`)
- `AUTH0_AUDIENCE`: The audience for your API (API identifier in Auth0)
- `AWS_ACCOUNT_ID`: Your AWS account ID
- `AWS_REGION`: AWS region (defaults to `us-east-1`)

Optional:
- `LOG_LEVEL`: Logging level (defaults to `INFO`)

## Deployment

### Using Serverless Framework

1. Install dependencies:
```bash
npm install -g serverless
npm install serverless-python-requirements
```

2. Set environment variables:
```bash
export AUTH0_DOMAIN=your-tenant.auth0.com
export AUTH0_AUDIENCE=your-api-identifier
export AWS_ACCOUNT_ID=************
```

3. Deploy:
```bash
serverless deploy --stage dev
```

### Manual Deployment

1. Install Python dependencies:
```bash
pip install -r requirements.txt -t .
```

2. Create deployment package:
```bash
zip -r auth0-authorizer.zip .
```

3. Deploy to AWS Lambda via AWS CLI or Console

## API Gateway Integration

### Using Serverless Framework

The authorizer will be automatically configured when deployed. Reference it in your API Gateway resources:

```yaml
functions:
  myApi:
    handler: handler.main
    events:
      - http:
          path: /reports/{customer}/{site}
          method: get
          authorizer:
            name: auth0Authorizer
            type: TOKEN
            identitySource: method.request.header.Authorization
```

### Manual Configuration

1. Create a Lambda Authorizer in API Gateway
2. Set the Lambda function ARN
3. Configure:
   - **Type**: TOKEN
   - **Identity Source**: `method.request.header.Authorization`
   - **TTL**: 300 seconds (recommended)

## Resource Patterns

The authorizer generates IAM policies for the following resource patterns:

### Reports Access
- `GET /reports/{customer}` - List reports for customer
- `GET /reports/{customer}/{site}` - Get specific site report
- `GET /reports/{customer}/{site}/*` - Get site report details

### Vision Config Access
- `GET /vision-config` - Get vision configuration
- `POST /vision-config` - Update vision configuration

## Policy Generation

The authorizer generates IAM policies with resource ARNs based on user claims:

```json
{
  "principalId": "user-123",
  "policyDocument": {
    "Version": "2012-10-17",
    "Statement": [{
      "Action": "execute-api:Invoke",
      "Effect": "Allow",
      "Resource": [
        "arn:aws:execute-api:region:account:api-id/stage/*/reports/customer1/site1",
        "arn:aws:execute-api:region:account:api-id/stage/*/reports/customer1/site2",
        "arn:aws:execute-api:region:account:api-id/stage/*/vision-config/*"
      ]
    }]
  },
  "context": {
    "sub": "user-123",
    "claims": "{...}",
    "permissions": "..."
  }
}
```

## Error Handling

The authorizer returns a DENY policy for:
- Invalid or expired JWT tokens
- Missing required environment variables
- JWKS fetch failures
- Users without appropriate claims

## Testing

### Local Testing

```python
import json
from main import lambda_handler

# Mock event
event = {
    "authorizationToken": "Bearer your-jwt-token",
    "methodArn": "arn:aws:execute-api:us-east-1:************:abcdef123/dev/GET/reports/customer1/site1"
}

result = lambda_handler(event, {})
print(json.dumps(result, indent=2))
```

### Integration Testing

Use tools like Postman or curl to test with real JWT tokens:

```bash
curl -H "Authorization: Bearer your-jwt-token" \
     https://your-api-gateway-url/dev/reports/customer1/site1
```

## Monitoring

The authorizer logs important events:
- Token validation results
- Authorization decisions
- Error conditions

Monitor CloudWatch logs for the Lambda function to troubleshoot issues.

## Security Considerations

1. **Token Validation**: Always validates JWT signature using Auth0's JWKS
2. **Least Privilege**: Generates minimal IAM policies based on user claims
3. **Error Handling**: Never exposes sensitive information in error responses
4. **Caching**: Uses API Gateway's built-in caching (TTL: 300s) for performance

## Common Issues

### "Unable to find appropriate signing key"
- Verify `AUTH0_DOMAIN` is correct
- Check that the JWT token has a valid `kid` header
- Ensure Auth0 JWKS endpoint is accessible

### "Invalid token: audience"
- Verify `AUTH0_AUDIENCE` matches the API identifier in Auth0
- Check that the JWT token has the correct `aud` claim

### "Authorization failed: Missing required environment variables"
- Ensure all required environment variables are set
- Check Lambda function configuration in AWS Console
